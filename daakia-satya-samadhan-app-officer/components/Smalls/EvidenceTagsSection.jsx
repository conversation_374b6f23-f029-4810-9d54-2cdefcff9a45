import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';

const EvidenceTagsSection = ({ 
  tagsOptions, 
  selectedTags, 
  onTagSelect,
  tagData 
}) => {
  const [selectedTagInfo, setSelectedTagInfo] = useState(null);
  const [showTooltip, setShowTooltip] = useState(false);

  const formatDuration = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''} ${remainingMinutes > 0 ? `and ${remainingMinutes} minute${remainingMinutes > 1 ? 's' : ''}` : ''}`;
    }
    return `${minutes} minute${minutes > 1 ? 's' : ''}`;
  };

  const getTagInfo = (tagType) => {
    const tag = tagsOptions.find(t => t.key === tagType);
    if (tag) {
      const tagDataItem = tagData.find(t => t.type === tagType);
      if (tagDataItem) {
        return {
          type: tag.value,
          packagingAdvice: tagDataItem.packagingAdvice,
          durationInMinutesToSubmit: tagDataItem.durationInMinutesToSubmit
        };
      }
    }
    return null;
  };

  return (
    <View style={styles.tagsContainer}>
      <Text style={styles.sectionTitle}>Evidence Tags</Text>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.tagsScrollView}
      >
        {tagsOptions.map((tag) => (
          <TouchableOpacity
            key={tag.key}
            style={[
              styles.tagCard,
              selectedTags.includes(tag.key) && styles.selectedTagCard
            ]}
            onPress={() => {
              if (selectedTags.includes(tag.key)) {
                onTagSelect(prev => prev.filter(t => t !== tag.key));
              } else {
                onTagSelect(prev => [...prev, tag.key]);
              }
            }}
          >
            <View style={styles.tagCardContent}>
              <Ionicons 
                name={selectedTags.includes(tag.key) ? "checkmark-circle" : "ellipse-outline"} 
                size={24} 
                color={selectedTags.includes(tag.key) ? Colors.primary : "#666"} 
              />
              <Text style={[
                styles.tagCardText,
                selectedTags.includes(tag.key) && styles.selectedTagCardText
              ]}>
                {tag.value}
              </Text>
              <TouchableOpacity
                onPress={() => {
                  const info = getTagInfo(tag.key);
                  if (info) {
                    setSelectedTagInfo(info);
                    setShowTooltip(true);
                  }
                }}
                style={styles.infoButton}
              >
                <Ionicons name="information-circle-outline" size={20} color={Colors.primary} />
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {selectedTags.length > 0 && (
        <View style={styles.selectedTagsContainer}>
          <Text style={styles.selectedTagsTitle}>Selected Tags:</Text>
          <View style={styles.selectedTagsList}>
            {selectedTags.map((tagKey) => {
              const tag = tagsOptions.find(t => t.key === tagKey);
              return (
                <View key={tagKey} style={styles.selectedTagItem}>
                  <Text style={styles.selectedTagText}>{tag.value}</Text>
                  <TouchableOpacity 
                    onPress={() => onTagSelect(prev => prev.filter(t => t !== tagKey))}
                    style={styles.removeTagButton}
                  >
                    <Ionicons name="close-circle" size={18} color="#666" />
                  </TouchableOpacity>
                </View>
              );
            })}
          </View>
        </View>
      )}

      <Modal
        visible={showTooltip}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowTooltip(false)}
      >
        {selectedTagInfo && (
          <TouchableOpacity 
            style={styles.tooltipOverlay}
            activeOpacity={1}
            onPress={() => setShowTooltip(false)}
          >
            <View style={styles.tooltipContent}>
              <View style={styles.tooltipHeader}>
                <Text style={styles.tooltipTitle}>{selectedTagInfo.type}</Text>
                <TouchableOpacity onPress={() => setShowTooltip(false)}>
                  <Ionicons name="close" size={24} color={Colors.black} />
                </TouchableOpacity>
              </View>
              
              <View style={styles.tooltipSection}>
                <Text style={styles.tooltipSectionTitle}>Submission Time</Text>
                <View style={styles.packagingDetails}>
                  <View style={styles.bulletPointContainer}>
                    <Text style={styles.bulletPoint}>•</Text>
                    <Text style={styles.tooltipText}>
                      Must be submitted within {formatDuration(selectedTagInfo.durationInMinutesToSubmit)}
                    </Text>
                  </View>
                  <View style={styles.bulletPointContainer}>
                    <Text style={styles.bulletPoint}>•</Text>
                    <Text style={styles.tooltipText}>
                      Time starts from the moment of collection
                    </Text>
                  </View>
                  <View style={styles.bulletPointContainer}>
                    <Text style={styles.bulletPoint}>•</Text>
                    <Text style={styles.tooltipText}>
                      Late submissions may affect evidence validity
                    </Text>
                  </View>
                  <View style={styles.bulletPointContainer}>
                    <Text style={styles.bulletPoint}>•</Text>
                    <Text style={styles.tooltipText}>
                      Plan your submission route in advance
                    </Text>
                  </View>
                </View>
              </View>

              <View style={styles.tooltipSection}>
                <Text style={styles.tooltipSectionTitle}>Packaging Requirements</Text>
                <View style={styles.packagingDetails}>
                  <View style={styles.bulletPointContainer}>
                    <Text style={styles.bulletPoint}>•</Text>
                    <Text style={styles.tooltipText}>
                      Container Type: {selectedTagInfo.packagingAdvice.containerType}
                    </Text>
                  </View>
                  <View style={styles.bulletPointContainer}>
                    <Text style={styles.bulletPoint}>•</Text>
                    <Text style={styles.tooltipText}>
                      Size: {selectedTagInfo.packagingAdvice.containerSize}
                    </Text>
                  </View>
                  <View style={styles.bulletPointContainer}>
                    <Text style={styles.bulletPoint}>•</Text>
                    <Text style={styles.tooltipText}>
                      Material: {selectedTagInfo.packagingAdvice.containerMaterial}
                    </Text>
                  </View>
                  <View style={styles.bulletPointContainer}>
                    <Text style={styles.bulletPoint}>•</Text>
                    <Text style={styles.tooltipText}>
                      Color: {selectedTagInfo.packagingAdvice.containerColor}
                    </Text>
                  </View>
                  <View style={styles.bulletPointContainer}>
                    <Text style={styles.bulletPoint}>•</Text>
                    <Text style={styles.tooltipText}>
                      Dimensions: {selectedTagInfo.packagingAdvice.containerDimensions}
                    </Text>
                  </View>
                  <View style={styles.bulletPointContainer}>
                    <Text style={styles.bulletPoint}>•</Text>
                    <Text style={styles.tooltipText}>
                      Quantity: {selectedTagInfo.packagingAdvice.containerQuantity}
                    </Text>
                  </View>
                </View>
              </View>

              <View style={styles.tooltipSection}>
                <Text style={styles.tooltipSectionTitle}>Packaging Instructions</Text>
                <View style={styles.packagingDetails}>
                  <View style={styles.bulletPointContainer}>
                    <Text style={styles.bulletPoint}>•</Text>
                    <Text style={styles.tooltipText}>
                      Use {selectedTagInfo.packagingAdvice.containerQuantity} {selectedTagInfo.packagingAdvice.containerType}(s) of {selectedTagInfo.packagingAdvice.containerSize} size
                    </Text>
                  </View>
                  <View style={styles.bulletPointContainer}>
                    <Text style={styles.bulletPoint}>•</Text>
                    <Text style={styles.tooltipText}>
                      Container should be made of {selectedTagInfo.packagingAdvice.containerMaterial} and {selectedTagInfo.packagingAdvice.containerColor} in color
                    </Text>
                  </View>
                  <View style={styles.bulletPointContainer}>
                    <Text style={styles.bulletPoint}>•</Text>
                    <Text style={styles.tooltipText}>
                      Each container should have dimensions of {selectedTagInfo.packagingAdvice.containerDimensions}
                    </Text>
                  </View>
                  <View style={styles.bulletPointContainer}>
                    <Text style={styles.bulletPoint}>•</Text>
                    <Text style={styles.tooltipText}>
                      Ensure proper sealing and labeling of all containers
                    </Text>
                  </View>
                  <View style={styles.bulletPointContainer}>
                    <Text style={styles.bulletPoint}>•</Text>
                    <Text style={styles.tooltipText}>
                      Handle with care to prevent contamination or damage
                    </Text>
                  </View>
                  <View style={styles.bulletPointContainer}>
                    <Text style={styles.bulletPoint}>•</Text>
                    <Text style={styles.tooltipText}>
                      Store in appropriate conditions until submission
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        )}
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  tagsContainer: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 14,
    fontFamily: 'Roboto_medium',
    color: Colors.black,
    marginBottom: 12,
  },
  tagsScrollView: {
    marginBottom: 12,
  },
  tagCard: {
    backgroundColor: '#f8f8f8',
    borderRadius: 30,
    paddingHorizontal: 10,
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    minWidth: 120,
    height: 40,
    justifyContent: 'center',
  },
  selectedTagCard: {
    backgroundColor: '#f0f7ff',
    borderColor: Colors.primary,
  },
  tagCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tagCardText: {
    fontSize: 13,
    fontFamily: 'Roboto',
    color: Colors.black,
    marginLeft: 6,
  },
  selectedTagCardText: {
    color: Colors.primary,
    fontFamily: 'Roboto_medium',
  },
  infoButton: {
    marginLeft: 8,
    padding: 4,
  },
  selectedTagsContainer: {
    marginTop: 12,
    padding: 10,
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
  },
  selectedTagsTitle: {
    fontSize: 14,
    fontFamily: 'Roboto_medium',
    color: Colors.black,
    marginBottom: 8,
  },
  selectedTagsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
  },
  selectedTagItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    borderRadius: 16,
    paddingHorizontal: 10,
    paddingVertical: 4,
  },
  selectedTagText: {
    color: 'white',
    fontSize: 13,
    fontFamily: 'Roboto',
    marginRight: 4,
  },
  removeTagButton: {
    padding: 2,
  },
  tooltipOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  tooltipContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    width: '90%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  tooltipHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    paddingBottom: 12,
  },
  tooltipTitle: {
    fontSize: 18,
    fontFamily: 'Roboto_bold',
    color: Colors.black,
  },
  tooltipSection: {
    marginBottom: 16,
  },
  tooltipSectionTitle: {
    fontSize: 16,
    fontFamily: 'Roboto_medium',
    color: Colors.primary,
    marginBottom: 8,
  },
  tooltipText: {
    fontSize: 14,
    fontFamily: 'Roboto',
    color: Colors.black,
    flex: 1,
    lineHeight: 20,
  },
  packagingDetails: {
    backgroundColor: '#f8f8f8',
    padding: 12,
    borderRadius: 8,
  },
  bulletPointContainer: {
    flexDirection: 'row',
    marginBottom: 8,
    paddingRight: 8,
  },
  bulletPoint: {
    fontSize: 14,
    color: Colors.black,
    marginRight: 8,
    width: 8,
  },
});

export default EvidenceTagsSection; 