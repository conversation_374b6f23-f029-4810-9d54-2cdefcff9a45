import axios from 'axios';
import Constants from 'expo-constants';
import { API_ENDPOINTS, AUTH_ERRORS, FILE_TYPES } from '../constants/auth';
import { storageService } from './storage';
import { STORAGE_KEYS } from '../constants/auth';
import { router } from 'expo-router';
import { Alert } from 'react-native';

const BASE_URL = Constants.expoConfig?.extra?.baseUrl;

if (!BASE_URL) {
  throw new Error(AUTH_ERRORS.BASE_URL_MISSING);
}

const createBaseInstance = (token = '') => {
  const instance = axios.create({
    baseURL: BASE_URL,
    headers: {
      Authorization: token ? `Bearer ${token}` : '',
    },
  });

  instance.interceptors.response.use(
    (response) => response,
    async (error) => {
      // Handle token-related errors (401 and 403)
      if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        // Show alert before logging out
        Alert.alert(
          "Session Expired",
          "Your session has expired. You will be logged out for security reasons.",
          [
            {
              text: "OK",
              onPress: async () => {
                // Clear auth data
                await storageService.clearAuth();
                // Navigate to welcome screen
                router.replace('(auth)/welcome');
              }
            }
          ],
          { cancelable: false }
        );
        throw new Error(AUTH_ERRORS.TOKEN_EXPIRED);
      }
      
      // Handle 404 separately - just throw the error without logging out
      if (error.response && error.response.status === 404) {
        throw new Error('Resource not found');
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

// Generic request handler
const makeRequest = async (method, endpoint, data = null, token = '', customHeaders = {}) => {
  const instance = createBaseInstance(token);
  const config = {
    headers: { ...customHeaders },
  };

  try {
    const response = method === 'get' 
      ? await instance[method](endpoint, config)
      : await instance[method](endpoint, data, config);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const apiService = {
  // ===== AUTHENTICATION & TOKEN MANAGEMENT =====
  login: (mobileNumber) => 
    makeRequest('post', API_ENDPOINTS.LOGIN, { mobileNumber }, '', { 'Category': 'court' }),

  verifyOtp: (userId, requestId, otp) => 
    makeRequest('post', API_ENDPOINTS.VERIFY_OTP, { userId, requestId, otp }),

  refreshToken: (userId, token) => 
    makeRequest('post', API_ENDPOINTS.REFRESH_TOKEN, { userId }, token),

  // ===== USER PROFILE MANAGEMENT =====
  fetchProfile: (token) => 
    makeRequest('get', API_ENDPOINTS.PROFILE, null, token),

  updateProfile: (token, updateData) => 
    makeRequest('put', API_ENDPOINTS.PROFILE, updateData, token),

  // ===== REGISTRATION & COURT MANAGEMENT =====
  register: (userData) => 
    makeRequest('post', API_ENDPOINTS.REGISTER, userData),

  fetchCourts: () => 
    makeRequest('get', API_ENDPOINTS.COURTS),

  fetchCourtDepartments: (courtId) => 
    makeRequest('get', API_ENDPOINTS.COURT_DEPARTMENTS.replace(':courtId', courtId)),

  // ===== FILE UPLOAD MANAGEMENT =====
  uploadFile: async (uri, type) => {
    const formData = new FormData();
    const fileType = FILE_TYPES[type.toUpperCase()] || FILE_TYPES.DEFAULT;

    const file = {
      uri,
      name: `file-${Date.now()}.${fileType.extension}`,
      type: fileType.mimeType,
    };

    formData.append('file', file);

    try {
      const response = await fetch(`${BASE_URL}${API_ENDPOINTS.UPLOAD}`, {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const result = await response.json();
      if (result.status === 'success') {
        return result.data.fileUrl;
      }
      throw new Error(result.message || AUTH_ERRORS.UPLOAD_FAILED);
    } catch (error) {
      throw new Error(error.message || AUTH_ERRORS.UPLOAD_ERROR);
    }
  },

  // ===== DISPATCHER MANAGEMENT =====
  fetchDispatcherRecentCases: (token) => 
    makeRequest('get', API_ENDPOINTS.DISPATCHER_RECENT_CASES, null, token),

  fetchForensicRequestEvidences: (token, forensicRequestId) => 
    makeRequest('get', `${API_ENDPOINTS.FORENSIC_REQUEST_EVIDENCES}/${forensicRequestId}/evidences`, null, token),

  dispatchForensicRequest: (token, forensicRequestId) => 
    makeRequest('put', `${API_ENDPOINTS.FORENSIC_REQUEST_DISPATCH}/${forensicRequestId}`, { status: 'dispatched' }, token),

  // ===== CASE MANAGEMENT =====
  fetchCaseDetails: (token, caseId) => 
    makeRequest('get', `${API_ENDPOINTS.CASE_DETAILS}/${caseId}`, null, token),

  fetchCourtCases: (token, page = 1, limit = 100) => 
    makeRequest('get', `${API_ENDPOINTS.FETCH_CASES}?page=${page}&limit=${limit}`, null, token),

  updateCaseEvidence: (token, caseId, evidenceId, updateData) => 
    makeRequest('put', `${API_ENDPOINTS.CASE_EVIDENCE_UPDATE}/${caseId}/evidences/${evidenceId}`, updateData, token),

  submitCaseToForensic: (token, caseId, evidenceIds) => 
    makeRequest('post', API_ENDPOINTS.CASE_SUBMIT_TO_FORENSIC.replace(':caseId', caseId), { evidences: evidenceIds }, token),

  submitEvidence: (token, caseId, data) => 
    makeRequest('post', `${API_ENDPOINTS.SUBMIT_EVIDENCE}/${caseId}/evidences`, data, token),

  updateCasePackage: (token, caseId, data) => 
    makeRequest('put', `${API_ENDPOINTS.UPDATE_CASE_PACKAGE}/${caseId}`, data, token),

  fetchEvidenceDetails: (token, caseId, evidenceId) => 
    makeRequest('get', `${API_ENDPOINTS.EVIDENCE_DETAILS}/${caseId}/evidences/${evidenceId}`, null, token),

  createCase: (token, data) => 
    makeRequest('post', API_ENDPOINTS.CREATE_CASE, data, token),

  updatePremises: (token, caseId, data) => 
    makeRequest('put', `${API_ENDPOINTS.UPDATE_PREMISES}/${caseId}`, data, token),

  // ===== FORENSIC REPORTS MANAGEMENT =====
  fetchForensicReports: (token, caseId) => 
    makeRequest('get', `${API_ENDPOINTS.FORENSIC_REPORTS}/${caseId}`, null, token),

  // ===== CASE SEARCH =====
  searchCaseByFIR: (token, firNumber) => 
    makeRequest('get', `${API_ENDPOINTS.CASE_SEARCH}/fir?firNumber=${firNumber}`, null, token),

  // ===== EVENTS MANAGEMENT =====
  createEvent: async (token, eventData) => {
    try {
      const response = await makeRequest('post', API_ENDPOINTS.CREATE_EVENT, eventData, token);
      return response;
    } catch (error) {
      console.error('Create event error:', error);
      if (error.response) {
        // Log the full error response for debugging
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },
};