import React, { useState, useEffect, useCallback, useMemo } from "react";
import {
  View,
  Text,
  useWindowDimensions,
  TouchableOpacity,
  Image,
  StyleSheet,
  FlatList,
  TextInput,
  ActivityIndicator,
  Alert,
} from "react-native";
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { useAuth } from "../../../../context/auth-context";
import { router, useFocusEffect, useRouter } from 'expo-router';
import { transformUrl } from "../../../../utils/transformUrl";
import PhotoGrid from "../../../../components/Smalls/PhotoGrid";
import EvidencesCard from "../../../../components/Larges/EvidencesCard";
import { Colors } from "../../../../constants/colors";
import { apiService } from "../../../../services/api";

const Tab1 = ({ caseid, changeTab }) => {
  const { width } = useWindowDimensions();
  const [premisesImages, setPremisesImages] = useState([]);
  const [caseDetails, setCaseDetails] = useState(null);
  const [evidences, setEvidences] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const { token } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const fetchData = useCallback(async () => {
    if (!caseid || !token) {
      setIsLoading(false);
      setError('Missing case ID or authentication token');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await apiService.fetchCaseDetails(token, caseid);
console.log(JSON.stringify(result,null,2));
      if (!result?.data) {
        throw new Error('Invalid data format received from server');
      }

      const premisesImageUrls = result.data.premisesImageUrl || [];
      const transformedImages = premisesImageUrls.map(url => 
        typeof url === 'string' ? transformUrl(url) : null
      ).filter(Boolean);
      
      setCaseDetails(result.data);
      setPremisesImages(transformedImages);
      setEvidences(result.data.evidences || []);
    } catch (err) {
      console.error('Error fetching case data:', err);
      setError(err.message || 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [caseid, token]);

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [fetchData])
  );

  const handleEvidenceClick = useCallback((evidenceId) => {
    if (evidenceId) {
      router.push({
        pathname: '(screens)/evidenceDetails',
        params: { evidenceId, caseid },
      });
    }
  }, [caseid]);

  const handleReportClick = useCallback(() => {
    if (typeof changeTab === 'function') {
      changeTab('tab2');
    }
  }, [changeTab]);

  const renderEvidenceMedia = useCallback((url) => {
    if (!url) return <View style={styles.evidenceImage} />;
    
    const transformedUrl = transformUrl(url);
    const isVideo = /\.(mp4|mov|avi|wmv|flv|webm|mkv)$/i.test(url);

    if (isVideo) {
      return (
        <View style={styles.evidenceImage}>
          <MaterialCommunityIcons name="play-circle" size={30} color={Colors.background} style={styles.playIcon} />
          <View style={styles.videoOverlay}>
            <Text style={styles.videoLabel}>VIDEO</Text>
          </View>
        </View>
      );
    } else {
      return (
        <Image
          source={{ uri: transformedUrl }}
          style={styles.evidenceImage}
          defaultSource={require('../../../../assets/images/small_satya_smadhanLogo.png')}
        />
      );
    }
  }, []);

  const renderItem = useCallback(({ item }) => {
    switch (item.type) {
      case 'premisesImages':
        if (!premisesImages || premisesImages.length === 0) {
          return null;
        }
        return (
          <View style={styles.sectionContainer}>
            <PhotoGrid 
              images={premisesImages}
              description={caseDetails?.premiseDescription || 'No description available'} 
              label='Premises Description'
            />
          </View>
        );

      case 'details':
        return (
          <View style={styles.detailsContainer}>
            <View style={styles.segmentedControlContainer}>
              <View style={styles.segmentedControl}>
                <TouchableOpacity
                  style={[styles.segmentButton, styles.activeSegment]}
                >
                  <MaterialCommunityIcons name="file-document-multiple-outline" size={18} color={Colors.background} />
                  <Text style={styles.activeSegmentText}>Evidences</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.segmentButton}
                  onPress={handleReportClick}
                >
                  <MaterialCommunityIcons name="clipboard-text-outline" size={18} color={Colors.lightText} />
                  <Text style={styles.segmentText}>Reports</Text>
                </TouchableOpacity>
              </View>
            </View>
            {[
              { label: 'Created By', value: caseDetails?.createdBy?.name || 'N/A' },
              { label: 'Created At', value: caseDetails?.createdAt ? new Date(caseDetails.createdAt).toLocaleString() : 'N/A' },
              { label: 'Title', value: caseDetails?.title || 'N/A' },
              { label: 'Description', value: caseDetails?.description || 'N/A' },
              { label: 'Fir Number', value: caseDetails?.firNumber || 'N/A' },
              { label: 'Case Type', value: caseDetails?.caseType || 'N/A' },
              { label: 'Address 1', value: caseDetails?.address1 || 'N/A' },
              { label: 'Address 2', value: caseDetails?.address2 || 'N/A' },
              { label: 'State', value: caseDetails?.state || 'N/A' },
              { label: 'City', value: caseDetails?.city || 'N/A' },
              { label: 'Pincode', value: caseDetails?.pincode || 'N/A' },
              { label: 'GPS Location', value: caseDetails?.gpsLocation || 'N/A' },
              { label: 'Remarks', value: caseDetails?.remarks || 'N/A' },
            ].map((detail, index) => (
              <View style={styles.detailRow} key={index}>
                <Text style={styles.label}>{detail.label}</Text>
                <Text style={styles.value}>{detail.value}</Text>
              </View>
            ))}
          </View>
        );

      case 'evidences':
        return (
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>Evidences</Text>
            {evidences && evidences.length > 0 ? (
              evidences.map((evidence, index) => (
                <EvidencesCard
                  key={evidence._id || index}
                  evidence={evidence}
                  onEvidenceClick={handleEvidenceClick}
                  isSubmitting={isSubmitting}
                />
              ))
            ) : (
              <Text style={styles.noEvidenceText}>No evidences found</Text>
            )}
          </View>
        );

      case 'scheduleHearing':
        return (
          <View style={styles.scheduleHearingContainer}>
            <TouchableOpacity
              style={[
                styles.commentButton,
                styles.scheduleHearingButton,
                isSubmitting && styles.disabledButton
              ]}
              onPress={() => {
                router.push({
                  pathname: '(screens)/scheduleHearing',
                  params: { caseid, openCreateModal: true },
                });
              }}
              disabled={isSubmitting}
            >
              <Text style={styles.commentButtonText}>SCHEDULE HEARING</Text>
            </TouchableOpacity>
          </View>
        );

      default:
        return null;
    }
  }, [
    premisesImages, 
    caseDetails, 
    evidences, 
    isSubmitting,
    handleReportClick,
    handleEvidenceClick,
    renderEvidenceMedia,
    caseid
  ]);

  const data = useMemo(() => [
    { type: 'premisesImages' },
    { type: 'details' },
    { type: 'evidences' },
    { type: 'scheduleHearing' },
  ], []);

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <MaterialCommunityIcons name="alert-circle-outline" size={48} color="#FF3B30" />
        <Text style={styles.errorText}>Error: {error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchData}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (isLoading) {
    return (
      <View style={styles.fullScreenLoader}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View style={styles.container}>
        <FlatList
          data={data}
          renderItem={renderItem}
          keyExtractor={(item, index) => `${item.type}-${index}`}
          contentContainerStyle={{ flexGrow: 1 }}
          initialNumToRender={3}
          maxToRenderPerBatch={5}
          windowSize={5}
        />
      </View>
    </GestureHandlerRootView>
  );
};

export default React.memo(Tab1);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  detailsContainer: {
    paddingHorizontal: '2%',
    paddingVertical: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  label: {
    fontSize: 14,
    opacity: 0.5,
    width: '40%',
    fontFamily: 'Roboto',
  },
  value: {
    fontSize: 14,
    width: '60%',
    fontWeight: '450',
    fontFamily: 'Roboto',
  },
  sectionContainer: {
    marginTop: 20,
  },
  sectionTitle: {
    textAlign: 'left',
    color: Colors.primary,
    fontWeight: '500',
    fontSize: 18,
    marginBottom: 14,
    marginHorizontal: '3%',
    fontFamily: 'Roboto_bold',
  },
  evidenceCard: {
    borderColor: Colors.border,
    borderWidth: 1,
    marginHorizontal: '2%',
    marginBottom: 24,
    padding: 12,
    borderRadius: 10,
    flexDirection: 'row',
    position: 'relative',
  },
  imageContainer: {
    width: 80,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  evidenceImage: {
    height: 80,
    width: 80,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
    resizeMode: 'cover',
  },
  evidenceDetails: {
    flex: 1,
    gap: 2,
  },
  evidenceDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  evidenceLabel: {
    fontSize: 12,
    color: Colors.lightText,
    width: 80,
  },
  evidenceType: {
    fontSize: 12,
    color: Colors.lightText,
    flex: 1,
  },
  evidenceTitle: {
    fontSize: 15,
    color: Colors.black,
    marginBottom: 4,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: Colors.background,
  },
  errorText: {
    color: '#FF3B30',
    marginVertical: 16,
    textAlign: 'center',
    fontSize: 16,
    fontFamily: 'Roboto',
  },
  retryButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryButtonText: {
    color: Colors.background,
    fontSize: 16,
    fontWeight: '500',
  },
  imageGrid: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: 100,
    height: 100,
    margin: 5,
    borderRadius: 8,
    resizeMode: 'cover',
  },
  fullScreenLoader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  segmentedControlContainer: {
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  segmentedControl: {
    flexDirection: 'row',
    borderRadius: 30,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: '#E8E8E8',
  },
  segmentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
    justifyContent: 'center',
    width: 150,
  },
  activeSegment: {
    backgroundColor: '#367E18',
    borderRadius: 30,
  },
  segmentText: {
    color: '#666666',
    marginLeft: 8,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  activeSegmentText: {
    color: '#FFFFFF',
    marginLeft: 8,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  noEvidenceText: {
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
    marginTop: 20,
    marginBottom: 40,
    fontFamily: 'Roboto',
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  videoLabel: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 4,
    fontFamily: 'Roboto',
  },
  scheduleHearingContainer: {
    paddingHorizontal: '30%',
    paddingVertical: 20,
    marginBottom: 20,
    alignItems: 'center',
  },
  scheduleHearingButton: {
    width: 300,
    alignSelf: 'center',
  },
  commentButton: {
    backgroundColor: Colors.primary,
    padding: 16,
    borderRadius: 50,
    alignItems: 'center',
    marginBottom: 16,
  },
  disabledButton: {
    backgroundColor: Colors.disabled,
    opacity: 0.7,
  },
  commentButtonText: {
    color: Colors.background,
    fontSize: 16,
    fontWeight: '500',
  },
  mediumBold: {
    fontWeight: '500',
  },
});