import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import CapturePageHeader from '../../../../components/Smalls/CapturePageHeader';
import CaptureButton from '../../../../components/Smalls/CaptureButton';
import OptionsOverlay from '../../../../components/Smalls/OptionsOverlay';
import MediaGrid from '../../../../components/Smalls/MediaGrid';

const Tab3 = ({ caseid }) => {
  const [showOptions, setShowOptions] = useState(false);
  const [media, setMedia] = useState([]);

  const handleUploadOption = (option) => {
    setShowOptions(false);
    Alert.alert('Coming Soon', 'This feature will be available soon!');
  };

  const handleDeleteMedia = (index) => {
    setMedia((prevMedia) => prevMedia.filter((_, i) => i !== index));
  };

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View style={styles.container}>
        <ScrollView style={styles.content} contentContainerStyle={{ paddingBottom: 20 }}>
          <CapturePageHeader
            title="Upload Documents"
            subtitle="Upload and manage all court-related documents for this case. Ensure each document is properly categorized and securely stored."
          />

          <CaptureButton 
            onPress={() => setShowOptions(true)} 
            title="Upload Documents"
          />

          {showOptions && (
            <OptionsOverlay
              visible={showOptions} 
              onSelectOption={handleUploadOption} 
              onClose={() => setShowOptions(false)} 
            />
          )}

          {media.length > 0 && (
            <MediaGrid
              media={media}
              onDeleteMedia={handleDeleteMedia}
              thumbnailSize={150}
            />
          )}
        </ScrollView>
      </View>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
  }
});

export default Tab3; 