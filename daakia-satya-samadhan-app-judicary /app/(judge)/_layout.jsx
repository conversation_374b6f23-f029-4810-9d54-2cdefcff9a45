import React from 'react';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import { Stack } from 'expo-router';
import CustomStackHeader from '../../components/Larges/CustomStackHeader';


export default function MainLayoutOfficer() {
  return (
    <SafeAreaProvider>
      <SafeAreaView style={{ flex: 1 }}>
        <Stack>
          <Stack.Screen
            name="(drawer)"
            options={{
              headerShown: false, 
            }}
          />

          {/* New Case Screen */}
         


<Stack.Screen
            name="(screens)/recentCases"
            options={{
              header: () => <CustomStackHeader title="Recent Cases" 
              showBackButton={true}
              backButtonColor="#979797"
              showSearchIcon={false}
              onSearchPress={() => console.log('Search Recent Cases')}
              showNotificationIcon={false}
              showBorder = {true} 
              borderColor = '#D8D8D8'
              // onNotificationPress={() => console.log('Notify Recent Cases')}
              />, 
            }}
          />

<Stack.Screen
            name="(screens)/caseDetails"
            options={{
              header: () => <CustomStackHeader title="Case Details" 
              showBackButton={true}
              backButtonColor="#979797"
              showSearchIcon={false}
              onSearchPress={() => console.log('Search Recent Cases')}
              showNotificationIcon={false}
              showBorder = {true} 
              borderColor = '#D8D8D8'
            
              />, 
            }}
          />
<Stack.Screen
            name="(screens)/evidenceDetails"
            options={{
              header: () => <CustomStackHeader title="Evidence Details" 
              
              showBackButton={true}
              backButtonColor="#979797"
              showSearchIcon={false}
              onSearchPress={() => console.log('Search Recent Cases')}
              showNotificationIcon={false}
              showBorder = {true} 
              borderColor = '#D8D8D8'
              // onNotificationPress={() => console.log('Notify Recent Cases')}
              
              />, 

              
            }}
          />



<Stack.Screen
            name="(screens)/scheduleHearing"
            options={{
              header: () => <CustomStackHeader title="Schedule Hearing" 
              
              showBackButton={true}
              backButtonColor="#979797"
              showSearchIcon={false}
              onSearchPress={() => console.log('Search Recent Cases')}
              showNotificationIcon={false}
              showBorder = {true} 
              borderColor = '#D8D8D8'
              // onNotificationPress={() => console.log('Notify Recent Cases')}
              
              />, 

              
            }}
          />












          <Stack.Screen
            name="(screens)/notifications"
            options={{
              header: () => <CustomStackHeader title="Notifications"
              
              showBackButton={true}
              backButtonColor="#979797"
              showSearchIcon={false}
              onSearchPress={() => console.log('Search Recent Cases')}
              showNotificationIcon={false}
              showBorder = {true} 
              borderColor = '#D8D8D8'
              // onNotificationPress={() => console.log('Notify Recent Cases')}
              
              />, 
            }}
          />

        </Stack>
      </SafeAreaView>
    </SafeAreaProvider>
  );
}