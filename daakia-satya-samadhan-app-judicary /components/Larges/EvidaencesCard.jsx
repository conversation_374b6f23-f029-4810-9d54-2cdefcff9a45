import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
} from 'react-native';
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import { Colors } from "../../constants/colors";
import { transformUrl } from "../../utils/transformUrl";

const EvidencesCard = ({ 
  evidence, 
  onEvidenceClick, 
  isSubmitting 
}) => {
  if (!evidence) return null;
  
  const firstAttachment = evidence.attachmentUrl?.[0] || null;

  const renderEvidenceMedia = (url) => {
    if (!url) return <View style={styles.evidenceImage} />;
    
    const transformedUrl = transformUrl(url);
    const isVideo = /\.(mp4|mov|avi|wmv|flv|webm|mkv)$/i.test(url);

    if (isVideo) {
      return (
        <View style={styles.evidenceImage}>
          <MaterialCommunityIcons name="play-circle" size={30} color={Colors.background} style={styles.playIcon} />
          <View style={styles.videoOverlay}>
            <Text style={styles.videoLabel}>VIDEO</Text>
          </View>
        </View>
      );
    } else {
      return (
        <Image
          source={{ uri: transformedUrl }}
          style={styles.evidenceImage}
          defaultSource={require('../../assets/images/small_satya_smadhanLogo.png')}
        />
      );
    }
  };

  return (
    <TouchableOpacity
      style={styles.evidenceCard}
      onPress={() => onEvidenceClick(evidence._id)}
      disabled={isSubmitting}
    >
      <View style={styles.imageContainer}>
        {renderEvidenceMedia(firstAttachment)}
      </View>
      <View style={styles.evidenceDetails}>
        <Text style={styles.evidenceTitle}>{evidence.title || 'Untitled Evidence'}</Text>
        <View style={styles.evidenceDetailRow}>
          <Text style={styles.evidenceLabel}>Type:</Text>
          <Text style={styles.evidenceType}>{evidence.type || 'Unknown'}</Text>
        </View>

        {evidence.lab_department && evidence.lab_department.length > 0 ? (
          <>
            {(() => {
              // Group departments by lab
              const labGroups = evidence.lab_department.reduce((acc, dept) => {
                const labId = dept.labId._id;
                if (!acc[labId]) {
                  acc[labId] = {
                    lab: dept.labId,
                    departments: []
                  };
                }
                acc[labId].departments.push(dept.labDepartmentId);
                return acc;
              }, {});

              return Object.values(labGroups).map((group, index) => (
                <React.Fragment key={group.lab._id}>
                  <View style={styles.evidenceDetailRow}>
                    <Text style={styles.evidenceLabel}>Lab {index + 1}:</Text>
                    <Text style={styles.evidenceType}>{group.lab.name}</Text>
                  </View>
                  <View style={styles.evidenceDetailRow}>
                    <Text style={styles.evidenceLabel}>Department:</Text>
                    <Text style={styles.evidenceType}>
                      {group.departments.map(dept => dept.name).join(', ')}
                    </Text>
                  </View>
                </React.Fragment>
              ));
            })()}
          </>
        ) : (
          <View style={styles.evidenceDetailRow}>
            <Text style={styles.evidenceLabel}>Lab:</Text>
            <Text style={styles.evidenceType}>Not sent to lab yet</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  evidenceCard: {
    borderColor: Colors.border,
    borderWidth: 1,
    marginHorizontal: '2%',
    marginBottom: 24,
    padding: 12,
    borderRadius: 10,
    flexDirection: 'row',
    position: 'relative',
  },
  imageContainer: {
    width: 80,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  evidenceImage: {
    height: 80,
    width: 80,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
    resizeMode: 'cover',
  },
  evidenceDetails: {
    flex: 1,
    gap: 2,
  },
  evidenceDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  evidenceLabel: {
    fontSize: 12,
    color: Colors.lightText,
    width: 80,
  },
  evidenceType: {
    fontSize: 12,
    color: Colors.lightText,
    flex: 1,
  },
  evidenceTitle: {
    fontSize: 15,
    color: Colors.black,
    marginBottom: 4,
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  videoLabel: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 4,
    fontFamily: 'Roboto',
  },
});

export default EvidencesCard; 