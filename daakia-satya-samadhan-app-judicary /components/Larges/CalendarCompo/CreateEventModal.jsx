import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  TouchableWithoutFeedback,
  Platform,
  ScrollView,
  Pressable,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../../../constants/colors';
import { IS_TABLET } from './styles/scheduleHearing.styles';
import * as DocumentPicker from 'expo-document-picker';
import DatePickerModal from './DatePickerModal';
import TimePickerModal from './TimePickerModal';
import TimezonePickerModal from './TimezonePickerModal';

// Event color options
const EVENT_COLORS = [
  { id: 'primary', color: '#0B36A1', label: 'Blue (Primary)' },
  { id: 'purple', color: '#6200ee', label: 'Purple' },
  { id: 'blue', color: '#1a73e8', label: 'Light Blue' },
  { id: 'green', color: '#34A853', label: 'Green' },
  { id: 'red', color: '#EA4335', label: 'Red' },
  { id: 'orange', color: '#FBBC04', label: 'Orange' },
];

const CreateEventModal = ({
  visible,
  onClose,
  onSave,
  initialDate,
  initialEndDate,
  editingEvent,
  isLoading = false,
}) => {
  const [title, setTitle] = useState('');
  const [details, setDetails] = useState('');
  // const [attendees, setAttendees] = useState([]);
  // const [attendeeName, setAttendeeName] = useState('');
  // const [showAttendeeInput, setShowAttendeeInput] = useState(false);

  // Date and time state
  const [eventDate, setEventDate] = useState(initialDate || new Date());
  const [eventEndDate, setEventEndDate] = useState(initialEndDate || new Date(new Date().getTime() + 60 * 60 * 1000));
  const [timezone, setTimezone] = useState({ id: 'UTC+05:30', label: '(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi' });

  // Event color state
  const [eventColor, setEventColor] = useState('#0B36A1'); // Default to primary color

  // Attachment state
  const [attachments, setAttachments] = useState([]);

  // Modal visibility state
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showStartTimePicker, setShowStartTimePicker] = useState(false);
  const [showEndTimePicker, setShowEndTimePicker] = useState(false);
  const [showTimezonePicker, setShowTimezonePicker] = useState(false);

  // Format date for display (e.g., "01/04/2025")
  const formatDate = (date) => {
    if (!date) return '';
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  // Format time for display (e.g., "13:00 PM")
  const formatTime = (date) => {
    if (!date) return '';
    const hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours % 12 || 12;
    return `${displayHours}:${minutes} ${ampm}`;
  };

  // Reset form when modal is opened
  useEffect(() => {
    if (visible) {
      if (editingEvent) {
        // Pre-fill form with existing event data
        setTitle(editingEvent.title || '');
        setDetails(editingEvent.details || '');
        setEventDate(editingEvent.start || new Date());
        setEventEndDate(editingEvent.end || new Date());
        setEventColor(editingEvent.color || '#0B36A1');
        setAttachments(editingEvent.attachments || []);
        if (editingEvent.timezone) {
          setTimezone({ id: editingEvent.timezone, label: `(UTC${editingEvent.timezone})` });
        }
      } else {
        // Reset form for new event
        setTitle('');
        setDetails('');
        setAttachments([]);
        setEventColor('#0B36A1'); // Reset to default color

        // Initialize date and time
        if (initialDate) {
          setEventDate(initialDate);
        }
        if (initialEndDate) {
          setEventEndDate(initialEndDate);
        } else if (initialDate) {
          // Default end time is 1 hour after start time
          setEventEndDate(new Date(initialDate.getTime() + 60 * 60 * 1000));
        }
      }
    }
  }, [visible, initialDate, initialEndDate, editingEvent]);

  // Handle document selection
  const handleDocumentSelection = async () => {
    try {
      // Launch document picker
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*', // All file types
        copyToCacheDirectory: true,
      });

      if (result.canceled === false && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        const newAttachment = {
          uri: asset.uri,
          name: asset.name,
          type: asset.mimeType || 'application/octet-stream',
          size: asset.size,
        };

        setAttachments(prev => [...prev, newAttachment]);
        Alert.alert('Success', 'Document attached successfully!');
      }
    } catch (error) {
      console.error('Error selecting document:', error);
      Alert.alert('Error', 'Failed to select document. Please try again.');
    }
  };

  const handleSave = () => {
    if (!title.trim() || isLoading) {
      // Don't save events without a title or when already loading
      return;
    }

    onSave({
      title,
      details,
      // attendees,
      start: eventDate,
      end: eventEndDate,
      timezone: timezone.id,
      color: eventColor,
      attachments: attachments,
    });

    // Don't close immediately - let the parent component handle closing after API success
  };

  // Handle date selection
  const handleDateSelect = (date) => {
    // Keep the time from the current eventDate
    const newDate = new Date(date);
    newDate.setHours(
      eventDate.getHours(),
      eventDate.getMinutes(),
      eventDate.getSeconds(),
      eventDate.getMilliseconds()
    );
    setEventDate(newDate);

    // Update end date to maintain the same duration
    const duration = eventEndDate.getTime() - eventDate.getTime();
    const newEndDate = new Date(newDate.getTime() + duration);
    setEventEndDate(newEndDate);
  };

  // Handle start time selection
  const handleStartTimeSelect = (hour, minute) => {
    const newDate = new Date(eventDate);
    newDate.setHours(hour, minute, 0, 0);
    setEventDate(newDate);

    // If end time is now before start time, adjust it
    if (eventEndDate <= newDate) {
      const newEndDate = new Date(newDate);
      newEndDate.setHours(newDate.getHours() + 1);
      setEventEndDate(newEndDate);
    }
  };

  // Handle end time selection
  const handleEndTimeSelect = (hour, minute) => {
    const newEndDate = new Date(eventEndDate);
    newEndDate.setHours(hour, minute, 0, 0);

    // Ensure end time is after start time
    if (newEndDate <= eventDate) {
      newEndDate.setDate(newEndDate.getDate() + 1);
    }

    setEventEndDate(newEndDate);
  };

  // const handleAddAttendee = () => {
  //   if (attendeeName.trim()) {
  //     setAttendees([...attendees, attendeeName.trim()]);
  //     setAttendeeName('');
  //     setShowAttendeeInput(false);
  //   }
  // };

  // const handleRemoveAttendee = (index) => {
  //   const newAttendees = [...attendees];
  //   newAttendees.splice(index, 1);
  //   setAttendees(newAttendees);
  // };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalOverlay}>
          <TouchableWithoutFeedback onPress={e => e.stopPropagation()}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>{editingEvent ? 'Edit Event' : 'New Event'}</Text>
                <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>
              </View>

              <ScrollView style={styles.formContainer}>
                {/* Save Button */}
                <TouchableOpacity
                  style={[
                    styles.saveButton,
                    (isLoading || !title.trim()) && styles.disabledSaveButton
                  ]}
                  onPress={handleSave}
                  disabled={isLoading || !title.trim()}
                >
                  {isLoading ? (
                    <ActivityIndicator size="small" color="white" style={styles.saveIcon} />
                  ) : (
                    <Ionicons name="save-outline" size={20} color="white" style={styles.saveIcon} />
                  )}
                  <Text style={styles.saveButtonText}>
                    {isLoading ? 'Creating...' : 'Save'}
                  </Text>
                </TouchableOpacity>

                {/* Title Input */}
                <View style={styles.inputRow}>
                  <Ionicons name="bookmark-outline" size={20} color="#666" style={styles.inputIcon} />
                  <TextInput
                    style={styles.titleInput}
                    placeholder="Add a title"
                    value={title}
                    onChangeText={setTitle}
                    placeholderTextColor="#999"
                  />
                </View>
                <View style={styles.separator} />

                {/* Attendees section commented out
                <View style={styles.inputRow}>
                  <Ionicons name="people-outline" size={20} color="#666" style={styles.inputIcon} />
                  {attendees.length > 0 ? (
                    <View style={styles.attendeesList}>
                      {attendees.map((attendee, index) => (
                        <View key={index} style={styles.attendeeChip}>
                          <Text style={styles.attendeeName}>{attendee}</Text>
                          <TouchableOpacity onPress={() => handleRemoveAttendee(index)}>
                            <Ionicons name="close-circle" size={18} color="#666" />
                          </TouchableOpacity>
                        </View>
                      ))}
                    </View>
                  ) : (
                    <Text style={styles.attendeesPlaceholder}>Invite attendees</Text>
                  )}

                  {!showAttendeeInput && (
                    <TouchableOpacity
                      style={styles.addAttendeeButton}
                      onPress={() => setShowAttendeeInput(true)}
                    >
                      <Ionicons name="add" size={20} color={Colors.primary} />
                    </TouchableOpacity>
                  )}
                </View>

                {showAttendeeInput && (
                  <View style={styles.attendeeInputContainer}>
                    <TextInput
                      style={styles.attendeeInput}
                      placeholder="Enter attendee name"
                      value={attendeeName}
                      onChangeText={setAttendeeName}
                      placeholderTextColor="#999"
                      autoFocus
                    />
                    <TouchableOpacity
                      style={styles.addButton}
                      onPress={handleAddAttendee}
                      disabled={!attendeeName.trim()}
                    >
                      <Text style={styles.addButtonText}>Add</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={styles.cancelButton}
                      onPress={() => {
                        setAttendeeName('');
                        setShowAttendeeInput(false);
                      }}
                    >
                      <Text style={styles.cancelButtonText}>Cancel</Text>
                    </TouchableOpacity>
                  </View>
                )}
                */}
                {/* <View style={styles.separator} /> */}

                {/* Date and Time */}
                <View style={styles.inputRow}>
                  <Ionicons name="time-outline" size={20} color="#666" style={styles.inputIcon} />
                  <View style={styles.dateTimeContainer}>
                    <TouchableOpacity
                      style={styles.dateContainer}
                      onPress={() => setShowDatePicker(true)}
                    >
                      <Text style={styles.dateText}>{formatDate(eventDate)}</Text>
                      <Ionicons name="calendar-outline" size={16} color="#666" style={styles.dateIcon} />
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={styles.timeContainer}
                      onPress={() => setShowStartTimePicker(true)}
                    >
                      <Text style={styles.timeText}>{formatTime(eventDate)}</Text>
                      <Ionicons name="chevron-down" size={16} color="#666" />
                    </TouchableOpacity>

                    <Text style={styles.toText}>to</Text>

                    <TouchableOpacity
                      style={styles.timeContainer}
                      onPress={() => setShowEndTimePicker(true)}
                    >
                      <Text style={styles.timeText}>{formatTime(eventEndDate)}</Text>
                      <Ionicons name="chevron-down" size={16} color="#666" />
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={styles.timezoneContainer}
                      onPress={() => setShowTimezonePicker(true)}
                    >
                      <Text style={styles.timezoneText}>{timezone.id} {timezone.label.split(' ')[1]}</Text>
                      <Ionicons name="chevron-down" size={16} color="#666" />
                    </TouchableOpacity>
                  </View>
                </View>
                <View style={styles.separator} />

                {/* Description */}
                <View style={styles.inputRow}>
                  <Ionicons name="document-text-outline" size={20} color="#666" style={styles.inputIcon} />
                  <View style={styles.descriptionContainer}>
                    <TextInput
                      style={styles.descriptionInput}
                      placeholder="Add a description"
                      value={details}
                      onChangeText={setDetails}
                      multiline
                      placeholderTextColor="#999"
                    />
                    <TouchableOpacity
                      style={styles.attachmentButton}
                      onPress={handleDocumentSelection}
                    >
                      <Ionicons name="document-attach-outline" size={24} color="#666" />
                    </TouchableOpacity>

                    {/* Show attachments if any */}
                    {attachments.length > 0 && (
                      <View style={styles.attachmentsContainer}>
                        <Text style={styles.attachmentCountText}>
                          {attachments.length} {attachments.length === 1 ? 'file' : 'files'} attached:
                        </Text>
                        {attachments.map((file, index) => (
                          <View key={index} style={styles.attachmentItem}>
                            <Ionicons name="document-outline" size={16} color={Colors.primary} style={styles.attachmentIcon} />
                            <Text style={styles.attachmentName} numberOfLines={1} ellipsizeMode="middle">
                              {file.name}
                            </Text>
                            <TouchableOpacity
                              style={styles.removeAttachmentButton}
                              onPress={() => {
                                const newAttachments = [...attachments];
                                newAttachments.splice(index, 1);
                                setAttachments(newAttachments);
                              }}
                            >
                              <Ionicons name="close-circle" size={18} color="#666" />
                            </TouchableOpacity>
                          </View>
                        ))}
                      </View>
                    )}
                  </View>
                </View>

                {/* Color Selection */}
                <View style={styles.inputRow}>
                  <Ionicons name="color-palette-outline" size={20} color="#666" style={styles.inputIcon} />
                  <View style={styles.colorContainer}>
                    <Text style={styles.colorLabel}>Event Color</Text>
                    <View style={styles.colorOptions}>
                      {EVENT_COLORS.map((colorOption) => (
                        <TouchableOpacity
                          key={colorOption.id}
                          style={[
                            styles.colorOption,
                            { backgroundColor: colorOption.color },
                            eventColor === colorOption.color && styles.selectedColorOption
                          ]}
                          onPress={() => setEventColor(colorOption.color)}
                        >
                          {eventColor === colorOption.color && (
                            <Ionicons name="checkmark" size={16} color="white" />
                          )}
                        </TouchableOpacity>
                      ))}
                    </View>
                  </View>
                </View>
              </ScrollView>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>

      {/* Date Picker Modal */}
      <DatePickerModal
        visible={showDatePicker}
        onClose={() => setShowDatePicker(false)}
        onDateSelect={handleDateSelect}
        selectedDate={eventDate}
      />

      {/* Start Time Picker Modal */}
      <TimePickerModal
        visible={showStartTimePicker}
        onClose={() => setShowStartTimePicker(false)}
        onTimeSelect={handleStartTimeSelect}
        initialHour={eventDate.getHours()}
        initialMinute={eventDate.getMinutes()}
      />

      {/* End Time Picker Modal */}
      <TimePickerModal
        visible={showEndTimePicker}
        onClose={() => setShowEndTimePicker(false)}
        onTimeSelect={handleEndTimeSelect}
        initialHour={eventEndDate.getHours()}
        initialMinute={eventEndDate.getMinutes()}
      />

      {/* Timezone Picker Modal */}
      <TimezonePickerModal
        visible={showTimezonePicker}
        onClose={() => setShowTimezonePicker(false)}
        onTimezoneSelect={setTimezone}
        initialTimezone={timezone.id}
      />
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: IS_TABLET ? '80%' : '95%',
    maxWidth: IS_TABLET ? 700 : 500,
    backgroundColor: Colors.background,
    borderRadius: 8,
    overflow: 'hidden',
    maxHeight: IS_TABLET ? '80%' : '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: Colors.black,
  },
  closeButton: {
    padding: 4,
  },
  formContainer: {
    padding: 16,
    maxHeight: IS_TABLET ? 500 : 600,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 4,
    alignSelf: 'flex-start',
    marginBottom: 20,
  },
  saveIcon: {
    marginRight: 8,
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  inputIcon: {
    marginRight: 16,
    marginTop: 2,
  },
  titleInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.black,
    padding: 0,
  },
  separator: {
    height: 1,
    backgroundColor: Colors.border,
    marginBottom: 16,
  },
  attendeesList: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  attendeeChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  attendeeName: {
    marginRight: 4,
    color: Colors.black,
  },
  attendeesPlaceholder: {
    color: Colors.lightText,
    flex: 1,
  },
  addAttendeeButton: {
    padding: 4,
  },
  attendeeInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 36,
    marginBottom: 16,
  },
  attendeeInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 4,
    padding: 8,
    marginRight: 8,
  },
  addButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
    marginRight: 8,
  },
  addButtonText: {
    color: 'white',
  },
  cancelButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  cancelButtonText: {
    color: '#666',
  },
  dateTimeContainer: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  dateText: {
    marginRight: 8,
    color: Colors.black,
  },
  dateIcon: {
    marginLeft: 4,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  timeText: {
    marginRight: 8,
    color: Colors.black,
  },
  toText: {
    marginRight: 8,
    marginBottom: 8,
    color: Colors.lightText,
  },
  timezoneContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 8,
  },
  timezoneText: {
    marginRight: 8,
    color: Colors.black,
  },
  descriptionContainer: {
    flex: 1,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 4,
    padding: 8,
    minHeight: 100,
    flexDirection: 'column',
  },
  descriptionInput: {
    flex: 1,
    textAlignVertical: 'top',
    color: Colors.black,
    padding: 0,
    minHeight: 80,
  },
  attachmentButton: {
    alignSelf: 'flex-end',
    padding: 4,
  },
  attachmentsContainer: {
    marginTop: 8,
    padding: 4,
  },
  attachmentCountText: {
    color: Colors.primary,
    fontSize: 14,
    marginBottom: 4,
  },
  attachmentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 6,
    marginVertical: 2,
  },
  attachmentIcon: {
    marginRight: 6,
  },
  attachmentName: {
    flex: 1,
    fontSize: 14,
    color: Colors.black,
  },
  removeAttachmentButton: {
    padding: 2,
  },
  colorContainer: {
    flex: 1,
  },
  colorLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.black,
    marginBottom: 8,
  },
  colorOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  colorOption: {
    width: 36,
    height: 36,
    borderRadius: 18,
    marginRight: 12,
    marginBottom: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedColorOption: {
    borderWidth: 2,
    borderColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2,
  },
  disabledSaveButton: {
    backgroundColor: '#ccc',
    opacity: 0.7,
  },
});

export default CreateEventModal;
