export const ROUTES = {
  // Auth routes
  LOGIN: '(auth)/login',
  OTP_VERIFICATION: '(auth)/verify-otp',
  REGISTER: '(auth)/register',
  WELCOME: '(auth)/welcome',
  THANK_YOU: '(auth)/thankYouPage',
  // Forensic routes
  RECENT_CASES: '(forensic)/(screens)/recentCases',
  CASE_DETAILS: '(forensic)/(screens)/caseDetails',
  EVIDENCE_DETAILS: '(forensic)/(screens)/evidancesDetails',
  PROFILE: '(forensic)/(screens)/profile',
  SETTINGS: '(forensic)/(screens)/settings',
  VIEW_ALL_DOCUMENTS: '(forensic)/(screens)/viewAllDocumnets',
  CAPTURE_REPORT: '(forensic)/(screens)/captureReport',

  // Common routes
  NOT_FOUND: '(common)/notFound',
  ERROR: '(common)/error',
};

// Route parameters configuration
// export const ROUTE_PARAMS = {
//   [ROUTES.CASE_DETAILS]: {
//     forensicRequestId: 'string',
//   },
//   [ROUTES.EVIDENCE_DETAILS]: {
//     evidenceId: 'string',
//     caseId: 'string',
//   },
//   [ROUTES.REPORT_SUBMISSION]: {
//     evidenceId: 'string',
//     caseId: 'string',
//   },
// }; 